import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import TreeGrid from '../TreeGrid';

// Mock AG Grid to avoid issues in test environment
vi.mock('ag-grid-react', () => ({
  AgGridReact: ({ rowData, columnDefs }: any) => (
    <div data-testid="ag-grid-mock">
      <div>Rows: {rowData?.length || 0}</div>
      <div>Columns: {columnDefs?.length || 0}</div>
    </div>
  )
}));

vi.mock('ag-grid-community', () => ({
  AllCommunityModule: {},
  ModuleRegistry: {
    registerModules: vi.fn()
  }
}));

describe('TreeGrid Component', () => {
  it('renders the tree grid component', () => {
    render(<TreeGrid />);
    
    // Check if the header is rendered
    expect(screen.getByText('File Explorer - Tree Grid')).toBeInTheDocument();
    expect(screen.getByText(/A tree-like table using AG Grid Community Edition/)).toBeInTheDocument();
  });

  it('renders expand and collapse buttons', () => {
    render(<TreeGrid />);
    
    expect(screen.getByText('Expand All')).toBeInTheDocument();
    expect(screen.getByText('Collapse All')).toBeInTheDocument();
  });

  it('shows the correct item count', () => {
    render(<TreeGrid />);
    
    // Should show some items (the exact number depends on initial expanded state)
    expect(screen.getByText(/Showing \d+ of \d+ items/)).toBeInTheDocument();
  });

  it('renders the AG Grid component', () => {
    render(<TreeGrid />);
    
    // Check if our mocked AG Grid is rendered
    expect(screen.getByTestId('ag-grid-mock')).toBeInTheDocument();
  });
});
