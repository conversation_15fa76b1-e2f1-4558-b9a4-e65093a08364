/* TreeGrid Component Styles */

.tree-grid-container {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.tree-grid-header h2 {
  color: #1a1a1a;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.tree-grid-header p {
  color: #666;
  margin-bottom: 0;
  line-height: 1.5;
}

.tree-controls button {
  transition: all 0.2s ease;
  font-weight: 500;
}

.tree-controls button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.tree-controls button:active {
  transform: translateY(0);
}

/* Custom AG Grid styling */
.ag-theme-alpine .ag-header {
  background: linear-gradient(to bottom, #f8f9fa, #e9ecef);
  border-bottom: 2px solid #dee2e6;
}

.ag-theme-alpine .ag-header-cell {
  font-weight: 600;
  color: #495057;
}

.ag-theme-alpine .ag-row {
  border-bottom: 1px solid #f1f3f4;
}

.ag-theme-alpine .ag-row:hover {
  background-color: #f8f9fa !important;
}

.ag-theme-alpine .ag-row.ag-row-selected {
  background-color: #e3f2fd !important;
}

.ag-theme-alpine .ag-cell {
  display: flex;
  align-items: center;
  padding: 8px 12px;
}

/* Tree row specific styling */
.tree-row {
  transition: background-color 0.15s ease;
}

/* Custom scrollbar for the grid */
.ag-theme-alpine .ag-body-viewport::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.ag-theme-alpine .ag-body-viewport::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.ag-theme-alpine .ag-body-viewport::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.ag-theme-alpine .ag-body-viewport::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Responsive design */
@media (max-width: 768px) {
  .tree-controls {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 0.75rem !important;
  }
  
  .tree-controls button {
    width: 100%;
    max-width: 200px;
  }
}
