import React, { useState, useMemo, useCallback } from 'react';
import { AgGridReact } from 'ag-grid-react';
import { AllCommunityModule, ModuleRegistry } from 'ag-grid-community';
import type { ColDef, GridOptions, ICellRendererParams } from 'ag-grid-community';
import { flatTreeData } from '../data/sampleTreeData';
import type { FlatTreeNode } from '../types/TreeData';

// Register all Community features
ModuleRegistry.registerModules([AllCommunityModule]);

// Import AG Grid CSS
import 'ag-grid-community/styles/ag-grid.css';
import 'ag-grid-community/styles/ag-theme-alpine.css';
import './TreeGrid.css';

const TreeGrid: React.FC = () => {
  const [rowData, setRowData] = useState<FlatTreeNode[]>(flatTreeData);
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());

  // Function to get visible rows based on expanded state
  const getVisibleRows = useCallback(() => {
    const visibleRows: FlatTreeNode[] = [];

    for (const row of flatTreeData) {
      const level = row.orgHierarchy.length - 1;

      if (level === 0) {
        // Root level items are always visible
        visibleRows.push(row);
      } else {
        // Check if all parent levels are expanded
        let isVisible = true;
        for (let i = 0; i < level; i++) {
          const parentPath = row.orgHierarchy.slice(0, i + 1).join('/');
          if (!expandedNodes.has(parentPath)) {
            isVisible = false;
            break;
          }
        }
        if (isVisible) {
          visibleRows.push(row);
        }
      }
    }

    return visibleRows;
  }, [expandedNodes]);

  // Update visible rows when expanded state changes
  React.useEffect(() => {
    setRowData(getVisibleRows());
  }, [expandedNodes, getVisibleRows]);

  // Function to toggle node expansion
  const toggleNode = useCallback((nodePath: string) => {
    setExpandedNodes(prev => {
      const newSet = new Set(prev);
      if (newSet.has(nodePath)) {
        newSet.delete(nodePath);
      } else {
        newSet.add(nodePath);
      }
      return newSet;
    });
  }, []);

  // Function to check if a node has children
  const hasChildren = useCallback((node: FlatTreeNode) => {
    const nodePath = node.orgHierarchy.join('/');
    return flatTreeData.some(item =>
      item.orgHierarchy.length > node.orgHierarchy.length &&
      item.orgHierarchy.slice(0, node.orgHierarchy.length).join('/') === nodePath
    );
  }, []);

  // Column definitions
  const columnDefs: ColDef[] = useMemo(() => [
    {
      field: 'name',
      headerName: 'Name',
      flex: 2,
      cellRenderer: (params: ICellRendererParams) => {
        const data = params.data as FlatTreeNode;
        const level = data.orgHierarchy.length - 1;
        const indent = level * 20; // 20px per level
        const isFolder = data.type === 'folder';
        const nodePath = data.orgHierarchy.join('/');
        const isExpanded = expandedNodes.has(nodePath);
        const nodeHasChildren = hasChildren(data);

        return (
          <div style={{
            paddingLeft: `${indent}px`,
            display: 'flex',
            alignItems: 'center',
            cursor: nodeHasChildren ? 'pointer' : 'default'
          }}>
            {nodeHasChildren && (
              <span
                onClick={(e) => {
                  e.stopPropagation();
                  toggleNode(nodePath);
                }}
                style={{
                  marginRight: '4px',
                  fontSize: '12px',
                  color: '#666',
                  userSelect: 'none',
                  width: '16px',
                  textAlign: 'center'
                }}
              >
                {isExpanded ? '▼' : '▶'}
              </span>
            )}
            {!nodeHasChildren && (
              <span style={{ width: '20px' }}></span>
            )}
            <span style={{
              marginRight: '8px',
              fontSize: '16px'
            }}>
              {isFolder ? (isExpanded ? '📂' : '📁') : '📄'}
            </span>
            <span>{data.name}</span>
          </div>
        );
      }
    },
    {
      field: 'type',
      headerName: 'Type',
      flex: 1,
      cellRenderer: (params: ICellRendererParams) => {
        const type = params.value;
        return (
          <span style={{
            padding: '4px 12px',
            borderRadius: '16px',
            fontSize: '11px',
            fontWeight: '500',
            textTransform: 'uppercase',
            backgroundColor: type === 'folder' ? '#e3f2fd' : '#f3e5f5',
            color: type === 'folder' ? '#1976d2' : '#7b1fa2',
            border: `1px solid ${type === 'folder' ? '#bbdefb' : '#e1bee7'}`
          }}>
            {type}
          </span>
        );
      }
    },
    {
      field: 'size',
      headerName: 'Size',
      flex: 1,
      cellStyle: { textAlign: 'right' },
      valueFormatter: (params) => {
        if (params.value === undefined || params.value === null) {
          return '-';
        }
        const bytes = params.value;
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
      }
    },
    {
      field: 'dateModified',
      headerName: 'Date Modified',
      flex: 1,
      valueFormatter: (params) => {
        return new Date(params.value).toLocaleDateString();
      }
    }
  ], [expandedNodes, hasChildren, toggleNode]);

  // Grid options
  const gridOptions: GridOptions = {
    defaultColDef: {
      sortable: true,
      filter: true,
      resizable: true,
      menuTabs: ['filterMenuTab', 'generalMenuTab'],
    },
    animateRows: true,
    rowSelection: 'multiple',
    suppressRowClickSelection: false,
    rowHeight: 45,
    headerHeight: 50,
    suppressHorizontalScroll: false,
    suppressColumnVirtualisation: false,
    enableRangeSelection: true,
    rowMultiSelectWithClick: true,
    suppressRowDeselection: false,
    rowClass: 'tree-row',
    getRowStyle: (params) => {
      const level = (params.data as FlatTreeNode).orgHierarchy.length - 1;
      return {
        backgroundColor: level % 2 === 0 ? '#ffffff' : '#fafafa'
      };
    }
  };

  // Function to expand all nodes
  const expandAll = useCallback(() => {
    const allPaths = new Set<string>();
    flatTreeData.forEach(item => {
      if (item.type === 'folder') {
        allPaths.add(item.orgHierarchy.join('/'));
      }
    });
    setExpandedNodes(allPaths);
  }, []);

  // Function to collapse all nodes
  const collapseAll = useCallback(() => {
    setExpandedNodes(new Set());
  }, []);

  return (
    <div className="tree-grid-container">
      <div className="tree-grid-header">
        <h2>File Explorer - Tree Grid</h2>
        <p>A tree-like table using AG Grid Community Edition with expand/collapse functionality</p>

        <div className="tree-controls" style={{
          marginTop: '1rem',
          display: 'flex',
          gap: '0.5rem',
          alignItems: 'center'
        }}>
          <button
            onClick={expandAll}
            style={{
              padding: '8px 16px',
              backgroundColor: '#1976d2',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            Expand All
          </button>
          <button
            onClick={collapseAll}
            style={{
              padding: '8px 16px',
              backgroundColor: '#757575',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            Collapse All
          </button>
          <span style={{ color: '#666', fontSize: '14px' }}>
            Showing {rowData.length} of {flatTreeData.length} items
          </span>
        </div>
      </div>

      <div
        className="ag-theme-alpine"
        style={{
          height: '600px',
          width: '100%',
          border: '1px solid #ddd',
          borderRadius: '8px',
          marginTop: '1rem',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
        }}
      >
        <AgGridReact
          rowData={rowData}
          columnDefs={columnDefs}
          gridOptions={gridOptions}
        />
      </div>
    </div>
  );
};

export default TreeGrid;
