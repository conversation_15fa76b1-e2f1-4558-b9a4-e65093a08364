import type { TreeNode, FlatTreeNode } from '../types/TreeData';

// Hierarchical tree data
export const hierarchicalData: TreeNode[] = [
  {
    id: '1',
    name: 'Documents',
    type: 'folder',
    dateModified: '2024-01-15',
    children: [
      {
        id: '2',
        name: 'Projects',
        type: 'folder',
        dateModified: '2024-01-10',
        children: [
          {
            id: '3',
            name: 'React App',
            type: 'folder',
            dateModified: '2024-01-08',
            children: [
              {
                id: '4',
                name: 'src',
                type: 'folder',
                dateModified: '2024-01-08',
                children: [
                  {
                    id: '5',
                    name: 'App.tsx',
                    type: 'file',
                    size: 1024,
                    dateModified: '2024-01-08'
                  },
                  {
                    id: '6',
                    name: 'index.tsx',
                    type: 'file',
                    size: 512,
                    dateModified: '2024-01-07'
                  }
                ]
              },
              {
                id: '7',
                name: 'package.json',
                type: 'file',
                size: 2048,
                dateModified: '2024-01-05'
              }
            ]
          },
          {
            id: '8',
            name: 'Vue App',
            type: 'folder',
            dateModified: '2024-01-12',
            children: [
              {
                id: '9',
                name: 'main.js',
                type: 'file',
                size: 768,
                dateModified: '2024-01-12'
              }
            ]
          }
        ]
      },
      {
        id: '10',
        name: 'Reports',
        type: 'folder',
        dateModified: '2024-01-14',
        children: [
          {
            id: '11',
            name: 'Q1-2024.pdf',
            type: 'file',
            size: 5120,
            dateModified: '2024-01-14'
          },
          {
            id: '12',
            name: 'Q4-2023.pdf',
            type: 'file',
            size: 4096,
            dateModified: '2023-12-31'
          }
        ]
      }
    ]
  },
  {
    id: '13',
    name: 'Downloads',
    type: 'folder',
    dateModified: '2024-01-16',
    children: [
      {
        id: '14',
        name: 'image.png',
        type: 'file',
        size: 3072,
        dateModified: '2024-01-16'
      },
      {
        id: '15',
        name: 'document.docx',
        type: 'file',
        size: 1536,
        dateModified: '2024-01-15'
      }
    ]
  }
];

// Function to convert hierarchical data to flat structure for AG Grid
export function convertToFlatData(nodes: TreeNode[], parentPath: string[] = []): FlatTreeNode[] {
  const flatData: FlatTreeNode[] = [];
  
  nodes.forEach(node => {
    const currentPath = [...parentPath, node.name];
    
    // Add current node
    flatData.push({
      id: node.id,
      name: node.name,
      type: node.type,
      size: node.size,
      dateModified: node.dateModified,
      orgHierarchy: currentPath
    });
    
    // Recursively add children
    if (node.children && node.children.length > 0) {
      flatData.push(...convertToFlatData(node.children, currentPath));
    }
  });
  
  return flatData;
}

// Export flat data for AG Grid
export const flatTreeData: FlatTreeNode[] = convertToFlatData(hierarchicalData);
