// TypeScript interfaces for tree data structure

export interface TreeNode {
  id: string;
  name: string;
  type: 'folder' | 'file';
  size?: number;
  dateModified: string;
  children?: TreeNode[];
  // AG Grid specific properties for tree data
  orgHierarchy?: string[];
}

export interface FlatTreeNode extends Omit<TreeNode, 'children'> {
  // For AG Grid tree data, we need a flat structure with hierarchy information
  orgHierarchy: string[];
}
